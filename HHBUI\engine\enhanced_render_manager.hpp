/**
** =====================================================================================
**
**       文件名称: enhanced_render_manager.hpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强渲染管理器 - 现代化C++17高性能渲染管理框架 （声明文件）
**
**       主要功能:
**       - 硬件加速优化与GPU资源管理
**       - 多线程渲染管线与并行处理
**       - 智能渲染质量控制与自适应调节
**       - 高级渲染状态管理与缓存
**       - 渲染命令队列与批处理优化
**       - 实时性能监控与自动调优
**       - 多重渲染目标与后处理管线
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11/12 API
**       - 多线程安全的渲染资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能GPU命令缓冲与调度
**       - 智能内存管理与资源池化
**       - 实时渲染管线性能分析
**       - 自适应渲染质量控制
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建增强渲染管理器系统
**                             2. 实现硬件加速优化
**                             3. 添加多线程渲染支持
**                             4. 集成智能质量控制
**
** =====================================================================================
*/
#pragma once
#include "dx11_render_manager.h"
#include "enhanced_exception.hpp"
#include "smart_ptr.hpp"
#include "enhanced_mem_pool.hpp"
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>
#include <future>
#include <unordered_map>

namespace HHBUI
{
	// 渲染质量级别
	enum class RenderQuality : int
	{
		Low = 0,        // 低质量（性能优先）
		Medium = 1,     // 中等质量
		High = 2,       // 高质量
		Ultra = 3,      // 超高质量（质量优先）
		Adaptive = 4    // 自适应质量
	};

	// 硬件加速特性
	enum class HardwareFeature : uint32_t
	{
		None = 0,
		MultiSampling = 1 << 0,         // 多重采样抗锯齿
		AnisotropicFiltering = 1 << 1,  // 各向异性过滤
		TessellationShaders = 1 << 2,   // 曲面细分着色器
		ComputeShaders = 1 << 3,        // 计算着色器
		GeometryShaders = 1 << 4,       // 几何着色器
		ConservativeRaster = 1 << 5,    // 保守光栅化
		VariableRateShading = 1 << 6,   // 可变速率着色
		RayTracing = 1 << 7             // 光线追踪
	};

	// 渲染命令类型
	enum class RenderCommandType : int
	{
		Draw = 0,
		DrawIndexed = 1,
		Dispatch = 2,
		CopyResource = 3,
		ClearRenderTarget = 4,
		SetRenderTarget = 5,
		SetShader = 6,
		SetBuffer = 7,
		SetTexture = 8
	};

	// 渲染命令结构
	struct RenderCommand
	{
		RenderCommandType type;
		uint64_t timestamp;
		std::function<void()> execute;
		std::string debug_name;
		
		RenderCommand(RenderCommandType t, std::function<void()> func, const std::string& name = "")
			: type(t), timestamp(0), execute(std::move(func)), debug_name(name) {}
	};

	// 增强渲染统计
	struct EnhancedRenderStats : public RenderStats
	{
		// 硬件信息
		std::string gpu_name;
		uint64_t gpu_memory_total;
		uint64_t gpu_memory_available;
		
		// 性能指标
		std::atomic<float> average_frame_time{ 0.0f };
		std::atomic<float> min_frame_time{ 0.0f };
		std::atomic<float> max_frame_time{ 0.0f };
		std::atomic<uint32_t> dropped_frames{ 0 };
		
		// 渲染管线统计
		std::atomic<uint32_t> command_queue_size{ 0 };
		std::atomic<uint32_t> batched_draws{ 0 };
		std::atomic<uint32_t> state_changes{ 0 };
		std::atomic<uint32_t> texture_switches{ 0 };
		std::atomic<uint32_t> shader_switches{ 0 };
		
		// 多线程统计
		std::atomic<uint32_t> worker_threads_active{ 0 };
		std::atomic<uint32_t> parallel_tasks_completed{ 0 };
		std::atomic<float> thread_utilization{ 0.0f };
		
		// 质量控制统计
		RenderQuality current_quality{ RenderQuality::Medium };
		std::atomic<uint32_t> quality_adjustments{ 0 };
		std::atomic<float> target_frame_rate{ 60.0f };
		
		void reset()
		{
			// 重置所有原子变量
			average_frame_time.store(0.0f);
			min_frame_time.store(0.0f);
			max_frame_time.store(0.0f);
			dropped_frames.store(0);
			command_queue_size.store(0);
			batched_draws.store(0);
			state_changes.store(0);
			texture_switches.store(0);
			shader_switches.store(0);
			worker_threads_active.store(0);
			parallel_tasks_completed.store(0);
			thread_utilization.store(0.0f);
			quality_adjustments.store(0);
			target_frame_rate.store(60.0f);
		}
	};

	// 渲染配置
	struct EnhancedRenderConfig
	{
		RenderQuality quality_level = RenderQuality::Medium;
		uint32_t hardware_features = 0;  // HardwareFeature 位掩码
		bool enable_multithreading = true;
		bool enable_command_batching = true;
		bool enable_adaptive_quality = true;
		bool enable_vsync = true;
		bool enable_debug_markers = false;
		
		// 多线程配置
		uint32_t worker_thread_count = 0;  // 0 = 自动检测
		uint32_t command_queue_size = 1024;
		
		// 质量控制配置
		float target_frame_rate = 60.0f;
		float quality_adjustment_threshold = 0.1f;  // 10%性能差异触发调整
		
		// 内存配置
		uint64_t gpu_memory_budget = 0;  // 0 = 自动检测
		uint32_t resource_pool_size = 256;
	};

	/**
	 * @brief 增强渲染管理器
	 * 扩展UIDx11RenderManager，提供现代化高性能渲染功能
	 */
	class UIEnhancedRenderManager : public UIDx11RenderManager
	{
	public:
		UIEnhancedRenderManager();
		virtual ~UIEnhancedRenderManager();

		// 增强初始化
		HRESULT InitializeEnhanced(const EnhancedRenderConfig& config, HWND hwnd = nullptr);
		void ShutdownEnhanced();

		// 硬件加速功能
		HRESULT EnableHardwareFeature(HardwareFeature feature);
		HRESULT DisableHardwareFeature(HardwareFeature feature);
		bool IsHardwareFeatureSupported(HardwareFeature feature) const;
		uint32_t GetSupportedHardwareFeatures() const;

		// 多线程渲染
		HRESULT BeginParallelFrame();
		HRESULT EndParallelFrame();
		HRESULT SubmitRenderCommand(const RenderCommand& command);
		HRESULT SubmitRenderCommands(const std::vector<RenderCommand>& commands);
		HRESULT FlushCommandQueue();

		// 渲染质量控制
		HRESULT SetRenderQuality(RenderQuality quality);
		RenderQuality GetRenderQuality() const;
		HRESULT EnableAdaptiveQuality(bool enable);
		HRESULT SetTargetFrameRate(float fps);

		// 高级渲染功能
		HRESULT BeginRenderPass(const std::string& pass_name);
		HRESULT EndRenderPass();
		HRESULT SetRenderTargets(const std::vector<ITexture*>& render_targets, ITexture* depth_stencil = nullptr);
		HRESULT ClearRenderTargets(const std::vector<float>& clear_colors);

		// 批处理优化
		HRESULT BeginBatch(const std::string& batch_name);
		HRESULT EndBatch();
		HRESULT OptimizeBatches();

		// 性能监控
		const EnhancedRenderStats& GetEnhancedStats() const;
		HRESULT ResetEnhancedStats();
		std::string GetPerformanceReport() const;
		HRESULT ExportPerformanceData(const std::wstring& file_path) const;

		// 调试功能
		HRESULT SetDebugMarker(const std::string& marker_name);
		HRESULT BeginDebugEvent(const std::string& event_name);
		HRESULT EndDebugEvent();
		HRESULT CaptureFrame(const std::wstring& output_path);

		// 资源管理
		HRESULT PrewarmResources();
		HRESULT TrimResources();
		uint64_t GetGPUMemoryBudget() const;
		uint64_t GetGPUMemoryUsage() const;
		float GetGPUMemoryPressure() const;

	private:
		// 初始化辅助函数
		HRESULT InitializeHardwareFeatures();
		HRESULT InitializeMultithreading();
		HRESULT InitializeQualityControl();
		HRESULT DetectHardwareCapabilities();

		// 多线程渲染
		void WorkerThreadMain();
		void ProcessCommandQueue();
		HRESULT ExecuteRenderCommand(const RenderCommand& command);

		// 质量控制
		void QualityControlThreadMain();
		HRESULT AdjustRenderQuality();
		HRESULT ApplyQualitySettings(RenderQuality quality);

		// 性能监控
		void UpdatePerformanceStats();
		void UpdateFrameTimeStats(float frame_time);

		// 批处理优化
		HRESULT OptimizeDrawCalls();
		HRESULT SortRenderCommands();

	private:
		// 配置和状态
		EnhancedRenderConfig m_config;
		EnhancedRenderStats m_enhanced_stats;
		std::atomic<bool> m_shutdown_requested{ false };
		std::atomic<bool> m_in_parallel_frame{ false };

		// 硬件特性
		uint32_t m_supported_features;
		uint32_t m_enabled_features;

		// 多线程渲染
		std::vector<std::thread> m_worker_threads;
		std::queue<RenderCommand> m_command_queue;
		std::mutex m_command_queue_mutex;
		std::condition_variable m_command_queue_cv;
		std::atomic<uint32_t> m_pending_commands{ 0 };

		// 质量控制
		std::thread m_quality_control_thread;
		std::mutex m_quality_mutex;
		std::condition_variable m_quality_cv;
		std::chrono::high_resolution_clock::time_point m_last_quality_check;

		// 性能监控
		std::vector<float> m_frame_time_history;
		std::mutex m_stats_mutex;
		std::chrono::high_resolution_clock::time_point m_frame_start_time;

		// 批处理
		std::string m_current_batch_name;
		std::vector<RenderCommand> m_current_batch_commands;
		bool m_in_batch{ false };

		// 调试
		std::stack<std::string> m_debug_event_stack;
		bool m_debug_mode{ false };

		// 资源管理
		ExEnhancedObjectPool<RenderCommand> m_command_pool;
		uint64_t m_gpu_memory_budget;
	};

	/**
	 * @brief 渲染管理器工厂
	 * 创建增强渲染管理器实例
	 */
	class UIEnhancedRenderFactory
	{
	public:
		static HRESULT CreateEnhancedRenderManager(UIEnhancedRenderManager** manager);
		static HRESULT GetRecommendedConfig(EnhancedRenderConfig& config);
		static bool IsEnhancedRenderingSupported();
		static std::vector<std::string> GetSupportedFeatures();
	};

}
