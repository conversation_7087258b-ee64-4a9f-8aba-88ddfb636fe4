/**
** =====================================================================================
**
**       文件名称: enhanced_render_manager.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强渲染管理器 - 现代化C++17高性能渲染管理框架 （实现文件）
**
** =====================================================================================
*/
#include "pch.h"
#include "enhanced_render_manager.hpp"
#include "render_profiler.h"
#include <algorithm>
#include <fstream>
#include <sstream>

namespace HHBUI
{
	UIEnhancedRenderManager::UIEnhancedRenderManager()
		: m_supported_features(0), m_enabled_features(0)
		, m_command_pool(ObjectPoolConfig{})
		, m_gpu_memory_budget(0)
	{
		m_frame_time_history.reserve(120); // 2秒历史记录（60fps）
	}

	UIEnhancedRenderManager::~UIEnhancedRenderManager()
	{
		ShutdownEnhanced();
	}

	HRESULT UIEnhancedRenderManager::InitializeEnhanced(const EnhancedRenderConfig& config, HWND hwnd)
	{
		try
		{
			m_config = config;

			// 首先初始化基础渲染管理器
			HRESULT hr = Initialize(RenderType::HYBRID, hwnd);
			if (FAILED(hr)) return hr;

			// 检测硬件能力
			hr = DetectHardwareCapabilities();
			if (FAILED(hr)) return hr;

			// 初始化硬件特性
			hr = InitializeHardwareFeatures();
			if (FAILED(hr)) return hr;

			// 初始化多线程渲染
			if (m_config.enable_multithreading)
			{
				hr = InitializeMultithreading();
				if (FAILED(hr)) return hr;
			}

			// 初始化质量控制
			if (m_config.enable_adaptive_quality)
			{
				hr = InitializeQualityControl();
				if (FAILED(hr)) return hr;
			}

			// 应用初始质量设置
			hr = ApplyQualitySettings(m_config.quality_level);
			if (FAILED(hr)) return hr;

			// 预热资源
			hr = PrewarmResources();
			if (FAILED(hr)) return hr;

			return S_OK;
		}
		catch (const EnhancedException& ex)
		{
			throw_enhanced(ex.status(), L"增强渲染管理器初始化失败: " + ex.message(),
				ExceptionSeverity::Critical, ExceptionCategory::Graphics);
		}
	}

	void UIEnhancedRenderManager::ShutdownEnhanced()
	{
		m_shutdown_requested.store(true);

		// 停止质量控制线程
		if (m_quality_control_thread.joinable())
		{
			m_quality_cv.notify_all();
			m_quality_control_thread.join();
		}

		// 停止工作线程
		if (!m_worker_threads.empty())
		{
			m_command_queue_cv.notify_all();
			for (auto& thread : m_worker_threads)
			{
				if (thread.joinable())
				{
					thread.join();
				}
			}
			m_worker_threads.clear();
		}

		// 清空命令队列
		{
			std::lock_guard<std::mutex> lock(m_command_queue_mutex);
			while (!m_command_queue.empty())
			{
				m_command_queue.pop();
			}
		}

		// 关闭基础渲染管理器
		Shutdown();
	}

	HRESULT UIEnhancedRenderManager::EnableHardwareFeature(HardwareFeature feature)
	{
		uint32_t feature_bit = static_cast<uint32_t>(feature);
		
		if (!(m_supported_features & feature_bit))
		{
			return E_FAIL; // 硬件不支持此特性
		}

		m_enabled_features |= feature_bit;

		// 根据特性类型应用相应设置
		switch (feature)
		{
		case HardwareFeature::MultiSampling:
			// 启用多重采样
			break;
		case HardwareFeature::AnisotropicFiltering:
			// 启用各向异性过滤
			break;
		default:
			break;
		}

		return S_OK;
	}

	HRESULT UIEnhancedRenderManager::DisableHardwareFeature(HardwareFeature feature)
	{
		uint32_t feature_bit = static_cast<uint32_t>(feature);
		m_enabled_features &= ~feature_bit;
		return S_OK;
	}

	bool UIEnhancedRenderManager::IsHardwareFeatureSupported(HardwareFeature feature) const
	{
		uint32_t feature_bit = static_cast<uint32_t>(feature);
		return (m_supported_features & feature_bit) != 0;
	}

	uint32_t UIEnhancedRenderManager::GetSupportedHardwareFeatures() const
	{
		return m_supported_features;
	}

	HRESULT UIEnhancedRenderManager::BeginParallelFrame()
	{
		if (m_in_parallel_frame.load())
		{
			return E_FAIL; // 已经在并行帧中
		}

		m_frame_start_time = std::chrono::high_resolution_clock::now();
		m_in_parallel_frame.store(true);

		// 开始基础帧渲染
		HRESULT hr = BeginFrame();
		if (FAILED(hr))
		{
			m_in_parallel_frame.store(false);
			return hr;
		}

		return S_OK;
	}

	HRESULT UIEnhancedRenderManager::EndParallelFrame()
	{
		if (!m_in_parallel_frame.load())
		{
			return E_FAIL; // 不在并行帧中
		}

		// 刷新所有待处理命令
		HRESULT hr = FlushCommandQueue();
		if (FAILED(hr)) return hr;

		// 结束基础帧渲染
		hr = EndFrame();
		if (FAILED(hr)) return hr;

		// 更新性能统计
		auto frame_end_time = std::chrono::high_resolution_clock::now();
		auto frame_duration = std::chrono::duration<float, std::milli>(frame_end_time - m_frame_start_time);
		UpdateFrameTimeStats(frame_duration.count());

		m_in_parallel_frame.store(false);
		return S_OK;
	}

	HRESULT UIEnhancedRenderManager::SubmitRenderCommand(const RenderCommand& command)
	{
		if (!m_config.enable_multithreading)
		{
			// 直接执行命令
			return ExecuteRenderCommand(command);
		}

		// 添加到命令队列
		{
			std::lock_guard<std::mutex> lock(m_command_queue_mutex);
			if (m_command_queue.size() >= m_config.command_queue_size)
			{
				return E_OUTOFMEMORY; // 队列已满
			}

			m_command_queue.push(command);
			m_pending_commands.fetch_add(1);
		}

		m_command_queue_cv.notify_one();
		return S_OK;
	}

	HRESULT UIEnhancedRenderManager::SubmitRenderCommands(const std::vector<RenderCommand>& commands)
	{
		if (!m_config.enable_multithreading)
		{
			// 直接执行所有命令
			for (const auto& command : commands)
			{
				HRESULT hr = ExecuteRenderCommand(command);
				if (FAILED(hr)) return hr;
			}
			return S_OK;
		}

		// 批量添加到命令队列
		{
			std::lock_guard<std::mutex> lock(m_command_queue_mutex);
			if (m_command_queue.size() + commands.size() > m_config.command_queue_size)
			{
				return E_OUTOFMEMORY; // 队列空间不足
			}

			for (const auto& command : commands)
			{
				m_command_queue.push(command);
			}
			m_pending_commands.fetch_add(static_cast<uint32_t>(commands.size()));
		}

		m_command_queue_cv.notify_all();
		return S_OK;
	}

	HRESULT UIEnhancedRenderManager::FlushCommandQueue()
	{
		if (!m_config.enable_multithreading)
		{
			return S_OK; // 单线程模式下无需刷新
		}

		// 等待所有命令执行完成
		while (m_pending_commands.load() > 0)
		{
			std::this_thread::yield();
		}

		return S_OK;
	}

	HRESULT UIEnhancedRenderManager::SetRenderQuality(RenderQuality quality)
	{
		std::lock_guard<std::mutex> lock(m_quality_mutex);
		
		if (m_enhanced_stats.current_quality != quality)
		{
			HRESULT hr = ApplyQualitySettings(quality);
			if (SUCCEEDED(hr))
			{
				m_enhanced_stats.current_quality = quality;
				m_enhanced_stats.quality_adjustments.fetch_add(1);
			}
			return hr;
		}

		return S_OK;
	}

	RenderQuality UIEnhancedRenderManager::GetRenderQuality() const
	{
		return m_enhanced_stats.current_quality;
	}

	HRESULT UIEnhancedRenderManager::EnableAdaptiveQuality(bool enable)
	{
		m_config.enable_adaptive_quality = enable;
		return S_OK;
	}

	HRESULT UIEnhancedRenderManager::SetTargetFrameRate(float fps)
	{
		m_config.target_frame_rate = fps;
		m_enhanced_stats.target_frame_rate.store(fps);
		return S_OK;
	}

	const EnhancedRenderStats& UIEnhancedRenderManager::GetEnhancedStats() const
	{
		return m_enhanced_stats;
	}

	HRESULT UIEnhancedRenderManager::ResetEnhancedStats()
	{
		m_enhanced_stats.reset();
		ResetRenderStats(); // 重置基础统计
		return S_OK;
	}

	std::string UIEnhancedRenderManager::GetPerformanceReport() const
	{
		std::stringstream report;
		
		report << "=== HHBUI Enhanced Render Manager Performance Report ===\n";
		report << "GPU: " << m_enhanced_stats.gpu_name << "\n";
		report << "GPU Memory: " << (m_enhanced_stats.gpu_memory_used / 1024 / 1024) << " MB / " 
			   << (m_enhanced_stats.gpu_memory_total / 1024 / 1024) << " MB\n";
		report << "Current Quality: " << static_cast<int>(m_enhanced_stats.current_quality) << "\n";
		report << "Average Frame Time: " << m_enhanced_stats.average_frame_time.load() << " ms\n";
		report << "Target Frame Rate: " << m_enhanced_stats.target_frame_rate.load() << " fps\n";
		report << "Draw Calls: " << m_enhanced_stats.draw_calls << "\n";
		report << "Triangles: " << m_enhanced_stats.triangles << "\n";
		report << "Batched Draws: " << m_enhanced_stats.batched_draws.load() << "\n";
		report << "State Changes: " << m_enhanced_stats.state_changes.load() << "\n";
		report << "Worker Threads: " << m_enhanced_stats.worker_threads_active.load() << "\n";
		report << "Thread Utilization: " << (m_enhanced_stats.thread_utilization.load() * 100.0f) << "%\n";
		report << "Quality Adjustments: " << m_enhanced_stats.quality_adjustments.load() << "\n";

		return report.str();
	}

	HRESULT UIEnhancedRenderManager::DetectHardwareCapabilities()
	{
		try
		{
			m_supported_features = 0;

			// 检测多重采样支持
			UINT quality_levels = 0;
			if (SUCCEEDED(GetD3D11Device()->CheckMultisampleQualityLevels(DXGI_FORMAT_R8G8B8A8_UNORM, 4, &quality_levels)) && quality_levels > 0)
			{
				m_supported_features |= static_cast<uint32_t>(HardwareFeature::MultiSampling);
			}

			// 检测计算着色器支持
			D3D11_FEATURE_DATA_D3D11_OPTIONS options = {};
			if (SUCCEEDED(GetD3D11Device()->CheckFeatureSupport(D3D11_FEATURE_D3D11_OPTIONS, &options, sizeof(options))))
			{
				m_supported_features |= static_cast<uint32_t>(HardwareFeature::ComputeShaders);
			}

			// 检测几何着色器支持（DX11默认支持）
			m_supported_features |= static_cast<uint32_t>(HardwareFeature::GeometryShaders);

			// 检测曲面细分着色器支持（DX11默认支持）
			m_supported_features |= static_cast<uint32_t>(HardwareFeature::TessellationShaders);

			// 获取GPU信息
			Microsoft::WRL::ComPtr<IDXGIDevice> dxgi_device;
			HRESULT hr = GetD3D11Device()->QueryInterface(IID_PPV_ARGS(&dxgi_device));
			if (SUCCEEDED(hr))
			{
				Microsoft::WRL::ComPtr<IDXGIAdapter> adapter;
				hr = dxgi_device->GetAdapter(&adapter);
				if (SUCCEEDED(hr))
				{
					DXGI_ADAPTER_DESC desc;
					hr = adapter->GetDesc(&desc);
					if (SUCCEEDED(hr))
					{
						// 转换GPU名称
						char gpu_name[256];
						WideCharToMultiByte(CP_UTF8, 0, desc.Description, -1, gpu_name, sizeof(gpu_name), nullptr, nullptr);
						m_enhanced_stats.gpu_name = gpu_name;
						m_enhanced_stats.gpu_memory_total = desc.DedicatedVideoMemory;
						m_gpu_memory_budget = desc.DedicatedVideoMemory * 0.8; // 使用80%作为预算
					}
				}
			}

			return S_OK;
		}
		catch (...)
		{
			return E_FAIL;
		}
	}

	HRESULT UIEnhancedRenderManager::InitializeMultithreading()
	{
		try
		{
			// 确定工作线程数量
			uint32_t thread_count = m_config.worker_thread_count;
			if (thread_count == 0)
			{
				thread_count = std::max(1u, std::thread::hardware_concurrency() - 1);
			}

			// 创建工作线程
			m_worker_threads.reserve(thread_count);
			for (uint32_t i = 0; i < thread_count; ++i)
			{
				m_worker_threads.emplace_back(&UIEnhancedRenderManager::WorkerThreadMain, this);
			}

			m_enhanced_stats.worker_threads_active.store(thread_count);
			return S_OK;
		}
		catch (...)
		{
			return E_FAIL;
		}
	}

	void UIEnhancedRenderManager::WorkerThreadMain()
	{
		while (!m_shutdown_requested.load())
		{
			std::unique_lock<std::mutex> lock(m_command_queue_mutex);
			m_command_queue_cv.wait(lock, [this] {
				return m_shutdown_requested.load() || !m_command_queue.empty();
			});

			if (m_shutdown_requested.load())
				break;

			if (!m_command_queue.empty())
			{
				RenderCommand command = m_command_queue.front();
				m_command_queue.pop();
				lock.unlock();

				// 执行命令
				ExecuteRenderCommand(command);
				m_pending_commands.fetch_sub(1);
				m_enhanced_stats.parallel_tasks_completed.fetch_add(1);
			}
		}
	}

	HRESULT UIEnhancedRenderManager::ExecuteRenderCommand(const RenderCommand& command)
	{
		try
		{
			if (m_debug_mode && !command.debug_name.empty())
			{
				SetDebugMarker(command.debug_name);
			}

			command.execute();
			return S_OK;
		}
		catch (const std::exception& ex)
		{
			// 记录错误但继续执行
			return E_FAIL;
		}
	}

	void UIEnhancedRenderManager::UpdateFrameTimeStats(float frame_time)
	{
		std::lock_guard<std::mutex> lock(m_stats_mutex);

		m_frame_time_history.push_back(frame_time);
		if (m_frame_time_history.size() > 120) // 保持2秒历史
		{
			m_frame_time_history.erase(m_frame_time_history.begin());
		}

		// 计算统计数据
		if (!m_frame_time_history.empty())
		{
			float sum = 0.0f;
			float min_time = m_frame_time_history[0];
			float max_time = m_frame_time_history[0];

			for (float time : m_frame_time_history)
			{
				sum += time;
				min_time = std::min(min_time, time);
				max_time = std::max(max_time, time);
			}

			m_enhanced_stats.average_frame_time.store(sum / m_frame_time_history.size());
			m_enhanced_stats.min_frame_time.store(min_time);
			m_enhanced_stats.max_frame_time.store(max_time);

			// 检查是否需要调整质量
			if (m_config.enable_adaptive_quality)
			{
				float current_fps = 1000.0f / (sum / m_frame_time_history.size());
				float target_fps = m_config.target_frame_rate;
				float performance_ratio = current_fps / target_fps;

				if (performance_ratio < (1.0f - m_config.quality_adjustment_threshold))
				{
					// 性能不足，降低质量
					m_quality_cv.notify_one();
				}
				else if (performance_ratio > (1.0f + m_config.quality_adjustment_threshold))
				{
					// 性能过剩，提高质量
					m_quality_cv.notify_one();
				}
			}
		}
	}

}
